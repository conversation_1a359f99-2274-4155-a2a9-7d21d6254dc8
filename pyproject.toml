[project]
name = "content-vector-engine"
version = "0.1.0"
description = "基于AI的内容向量引擎"
requires-python = ">=3.11"
dependencies = [
    "langchain>=0.3.25",
    "langchain-community>=0.3.25",
    "langchain-core>=0.3.65",
    "langchain-huggingface>=0.3.0",
    "langchain-milvus>=0.2.0",
    "langgraph>=0.4.8",
    "pydantic>=2.11.7",
    "pydantic-settings>=2.9.1",
    "pymilvus>=2.5.11",
    "sentence-transformers>=4.1.0",
    "toml>=0.10.2",
    "loguru>=0.7.0",
    "rank-bm25>=0.2.2",
    "sqlalchemy>=2.0.0",
    "pymysql>=1.1.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.2",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.9.1",
    "isort>=5.12.0",
    "ruff>=0.1.0",
]

[tool.setuptools.packages.find]
include = ["src*"]
