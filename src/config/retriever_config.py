from typing import Optional

from pydantic import BaseModel, Field

from .base import TOML_CONFIG


class RetrieverConfig(BaseModel):
    """Retriever 配置"""

    use_bm25: bool = Field(
        default=TOML_CONFIG.get("retriever", {}).get("use_bm25", False),
        description="是否使用 BM25 进行检索",
    )

    bm25_weight: float = Field(
        default=TOML_CONFIG.get("retriever", {}).get("bm25_weight", 0.5),
        description="BM25 权重",
    )

    vector_weight: float = Field(
        default=TOML_CONFIG.get("retriever", {}).get("vector_weight", 0.5),
        description="向量检索权重",
    )

    reranker_model: str = Field(
        default=TOML_CONFIG.get("retriever", {}).get("reranker_model", ""),
        description="重排序模型名称",
    )

    rerank_top_k: int = Field(
        default=TOML_CONFIG.get("retriever", {}).get("rerank_top_k", 10),
        description="重排序 Top K",
    )

    score_threshold: Optional[float] = Field(
        default=TOML_CONFIG.get("retriever", {}).get("score_threshold", None),
        description="相似度阈值，低于此值的结果将被过滤",
    )
