from typing import Optional

from langchain_core.vectorstores import VectorStore
from langchain_milvus import Milvus

from src.config import appConfig
from src.embedding.embedding_client import get_embedding


def get_milvus_store(
    collection_name: str,
    text_field: str = "text",
    vector_field: str = "vector",
    metadata_field: Optional[str] = None,
) -> VectorStore:
    """根据 collection name 获取 Milvus VectorStore

    Args:
        collection_name: 集合名称
        text_field: 文本字段名称，默认为 "text"
        vector_field: 向量字段名称，默认为 "vector"
        metadata_field: 元数据字段名称，默认为 None（使用动态字段）
    """
    embedding = get_embedding()
    return Milvus(
        embedding_function=embedding,
        connection_args={
            "uri": appConfig.vector.uri,
            "user": appConfig.vector.user,
            "password": appConfig.vector.password,
            "db_name": appConfig.vector.db_name,
            "token": appConfig.vector.token,
            "timeout": appConfig.vector.timeout,
        },
        collection_name=collection_name,
        text_field=text_field,
        vector_field=vector_field,
        metadata_field=metadata_field,
    )
