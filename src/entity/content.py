from sqlalchemy import DECIMAL, Column, DateTime, String, Text

from src.db.base import Base


class Content(Base):
    """内容模型"""

    __tablename__ = "cms_base_content"

    # 内容编码，唯一键
    content_code = Column(String(50), primary_key=True, nullable=False)
    # 类型
    type = Column(String(50), nullable=False)
    # 标题
    title = Column(String(500), nullable=True)
    # 总结
    summary = Column(Text, nullable=True)
    # 高光
    highlight = Column(Text, nullable=True)
    # 评分
    rating = Column(DECIMAL(3, 1), nullable=True)
    # 区域
    region = Column(String(500), nullable=True)
    # 标签
    tags = Column(String(500), nullable=True)
    # 海报
    poster = Column(String(500), nullable=True)
    # 导演
    director = Column(Text, nullable=True)
    # 演员
    actors = Column(Text, nullable=True)
    # 创建时间
    create_time = Column(DateTime, nullable=True)
    # 年份
    year = Column(String(12), nullable=True)

    def __repr__(self):
        return (
            f"<Content(content_code={self.content_code}, type='{self.type}', title='{self.title}',"
            f" summary='{self.summary}', highlight='{self.highlight}', rating={self.rating},"
            f" region='{self.region}', tags='{self.tags}', poster='{self.poster}',"
            f" director='{self.director}', actors='{self.actors}', create_time={self.create_time},"
            f" year='{self.year}')>"
        )

    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            "content_code": self.content_code,
            "type": self.type,
            "title": self.title,
            "summary": self.summary,
            "highlight": self.highlight,
            "rating": float(self.rating) if self.rating else None,
            "region": self.region,
            "tags": self.tags,
            "poster": self.poster,
            "director": self.director,
            "actors": self.actors,
            "create_time": self.create_time.isoformat() if self.create_time else None,
            "year": self.year,
        }
