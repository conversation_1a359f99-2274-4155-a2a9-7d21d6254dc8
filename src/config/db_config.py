from pydantic import BaseModel, Field

from .base import TOML_CONFIG


class DatabaseConfig(BaseModel):
    """数据库配置"""

    host: str = Field(
        default=TOML_CONFIG.get("database", {}).get("host", "localhost"),
        description="数据库主机地址",
    )

    port: int = Field(
        default=TOML_CONFIG.get("database", {}).get("port", 3306),
        description="数据库端口",
    )

    username: str = Field(
        default=TOML_CONFIG.get("database", {}).get("username", "root"),
        description="数据库用户名",
    )

    password: str = Field(
        default=TOML_CONFIG.get("database", {}).get("password", ""),
        description="数据库密码",
    )

    database: str = Field(
        default=TOML_CONFIG.get("database", {}).get("database", "content_vector"),
        description="数据库名称",
    )

    charset: str = Field(
        default=TOML_CONFIG.get("database", {}).get("charset", "utf8mb4"),
        description="数据库字符集",
    )

    pool_size: int = Field(
        default=TOML_CONFIG.get("database", {}).get("pool_size", 10),
        description="连接池大小",
    )

    max_overflow: int = Field(
        default=TOML_CONFIG.get("database", {}).get("max_overflow", 20),
        description="连接池最大溢出数量",
    )

    pool_timeout: int = Field(
        default=TOML_CONFIG.get("database", {}).get("pool_timeout", 30),
        description="连接池超时时间（秒）",
    )

    pool_recycle: int = Field(
        default=TOML_CONFIG.get("database", {}).get("pool_recycle", 3600),
        description="连接池回收时间（秒）",
    )

    echo: bool = Field(
        default=TOML_CONFIG.get("database", {}).get("echo", False),
        description="是否打印SQL语句",
    )

    @property
    def database_url(self) -> str:
        """构建数据库连接URL"""
        return f"mysql+pymysql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}?charset={self.charset}"
