import unittest
from typing import List

from src.entity import Content
from src.repo.content_repository import ContentRepository


class TestContentSearch(unittest.TestCase):
    """测试内容搜索"""

    def test_search_limit_10(self):
        """测试搜索结果数量限制"""
        content_repository = ContentRepository()
        content_list: List[Content] = content_repository.get_all(limit=10)
        print(content_list)
