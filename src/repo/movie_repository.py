from datetime import datetime
from typing import List, Optional

from src.db.base_repository import BaseRepository
from src.entity.movie import Movie


class MovieRepository(BaseRepository[Movie]):
    """电影数据库操作类"""

    def __init__(self):
        super().__init__(Movie)

    def get_by_douban_id(self, douban_id: str) -> Optional[Movie]:
        """根据豆瓣ID获取电影"""
        return self.get_by_field("douban_id", douban_id)

    def search_by_title(
        self, title: str, skip: int = 0, limit: int = 100
    ) -> List[Movie]:
        """根据标题搜索电影"""
        search_fields = ["title", "foreign_title", "original_title"]
        return self.search(search_fields, title, skip, limit)

    def get_by_year(self, year: str, skip: int = 0, limit: int = 100) -> List[Movie]:
        """根据年份获取电影"""
        return self.get_by_filters({"year": year}, skip, limit)

    def get_by_genre(self, genre: str, skip: int = 0, limit: int = 100) -> List[Movie]:
        """根据类型获取电影（模糊匹配）"""
        return self.search(["genres"], genre, skip, limit)

    def get_by_director(
        self, director: str, skip: int = 0, limit: int = 100
    ) -> List[Movie]:
        """根据导演获取电影（模糊匹配）"""
        return self.search(["directors"], director, skip, limit)

    def get_by_actor(self, actor: str, skip: int = 0, limit: int = 100) -> List[Movie]:
        """根据演员获取电影（模糊匹配）"""
        return self.search(["actors"], actor, skip, limit)

    def get_by_score_range(
        self, min_score: float, max_score: float, skip: int = 0, limit: int = 100
    ) -> List[Movie]:
        """根据评分范围获取电影"""
        from src.db.mysql_client import get_db_session

        with get_db_session() as session:
            query = session.query(Movie).filter(
                Movie.score >= min_score, Movie.score <= max_score
            )
            results = query.offset(skip).limit(limit).all()
            # 触发所有属性的加载，避免 DetachedInstanceError
            for result in results:
                session.expunge(result)
            return results

    def get_recent_movies(
        self, days: int = 30, skip: int = 0, limit: int = 100
    ) -> List[Movie]:
        """获取最近添加的电影"""
        from datetime import datetime, timedelta

        from src.db.mysql_client import get_db_session

        cutoff_date = datetime.now() - timedelta(days=days)

        with get_db_session() as session:
            query = (
                session.query(Movie)
                .filter(Movie.created_at >= cutoff_date)
                .order_by(Movie.created_at.desc())
            )
            results = query.offset(skip).limit(limit).all()
            # 触发所有属性的加载，避免 DetachedInstanceError
            for result in results:
                session.expunge(result)
            return results

    def update_movie_score(
        self, movie_id: int, score: float, votes: str
    ) -> Optional[Movie]:
        """更新电影评分和投票数"""
        update_data = {"score": score, "votes": votes, "updated_at": datetime.now()}
        return self.update(movie_id, update_data)

    def exists_by_douban_id(self, douban_id: str) -> bool:
        """检查豆瓣ID是否已存在"""
        return self.exists_by_field("douban_id", douban_id)

    def get_movies_without_cover(self, skip: int = 0, limit: int = 100) -> List[Movie]:
        """获取没有封面的电影"""
        from src.db.mysql_client import get_db_session

        with get_db_session() as session:
            query = session.query(Movie).filter(
                (Movie.cover_url.is_(None)) | (Movie.cover_url == "")
            )
            results = query.offset(skip).limit(limit).all()
            # 触发所有属性的加载，避免 DetachedInstanceError
            for result in results:
                session.expunge(result)
            return results

    def get_movies_by_countries(
        self, countries: List[str], skip: int = 0, limit: int = 100
    ) -> List[Movie]:
        """根据国家/地区获取电影（模糊匹配）"""
        from sqlalchemy import or_

        from src.db.mysql_client import get_db_session

        with get_db_session() as session:
            conditions = []
            for country in countries:
                conditions.append(Movie.countries.like(f"%{country}%"))

            query = session.query(Movie)
            if conditions:
                query = query.filter(or_(*conditions))

            results = query.offset(skip).limit(limit).all()
            # 触发所有属性的加载，避免 DetachedInstanceError
            for result in results:
                session.expunge(result)
            return results

    def get_top_rated_movies(
        self, min_votes: int = 1000, skip: int = 0, limit: int = 100
    ) -> List[Movie]:
        """获取高评分电影"""
        from src.db.mysql_client import get_db_session

        with get_db_session() as session:
            # 这里假设 votes 字段存储的是数字字符串，需要转换
            query = (
                session.query(Movie)
                .filter(Movie.score.isnot(None), Movie.votes.isnot(None))
                .order_by(Movie.score.desc())
            )

            results = query.offset(skip).limit(limit).all()
            # 触发所有属性的加载，避免 DetachedInstanceError
            for result in results:
                session.expunge(result)
            return results


# 创建全局实例
movie_repository = MovieRepository()
